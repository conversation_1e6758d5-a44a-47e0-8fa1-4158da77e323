# 表格位置和边框优化

## 修改内容

### 1. 表格边框改为细线
- **修改位置**: `setTableBorders()` 方法中的边框宽度设置
- **修改前**: `BigInteger.valueOf(4)` (4磅粗线)
- **修改后**: `BigInteger.valueOf(2)` (2磅细线)
- **效果**: 表格边框更加精细，不会过于突出

### 2. 表格插入位置优化
- **问题**: 表格插入到了文档最后，而不是"四、最高限价"部分
- **解决方案**: 改进表格插入逻辑，优先查找并替换`${projectContent}`占位符
- **备选方案**: 如果没有占位符，在"四、最高限价"段落后插入

## 技术实现

### 1. 细线边框设置
```java
// 所有边框都改为2磅细线
cellBorders.getTop().setSz(BigInteger.valueOf(2));
cellBorders.getBottom().setSz(BigInteger.valueOf(2));
cellBorders.getLeft().setSz(BigInteger.valueOf(2));
cellBorders.getRight().setSz(BigInteger.valueOf(2));
```

### 2. 智能表格插入逻辑
```java
// 1. 优先查找${projectContent}占位符
for (XWPFParagraph paragraph : document.getParagraphs()) {
    if (text.contains("${projectContent}")) {
        // 替换为"详见下表："
        paragraph.getRuns().clear();
        paragraph.createRun().setText("详见下表：");
        foundProjectContent = true;
        break;
    }
}

// 2. 如果没找到占位符，查找"四、最高限价"
if (!foundProjectContent) {
    for (XWPFParagraph paragraph : document.getParagraphs()) {
        if (text.contains("四、最高限价")) {
            // 在此段落后添加说明
            XWPFParagraph newParagraph = document.createParagraph();
            newParagraph.createRun().setText("详见下表：");
            break;
        }
    }
}

// 3. 创建表格（自动添加到正确位置）
XWPFTable table = document.createTable();
```

## 插入位置逻辑

### 优先级顺序
1. **第一优先**: 查找并替换`${projectContent}`占位符
2. **第二优先**: 在"四、最高限价"段落后插入
3. **最后备选**: 在文档末尾创建新的"四、最高限价"章节

### 模板兼容性
- **有占位符的模板**: 表格会精确替换`${projectContent}`位置
- **无占位符的模板**: 表格会插入到"四、最高限价"章节后
- **空白模板**: 会自动创建完整的章节结构

## 预期效果

### 1. 边框效果
```
┌─────┬──────────────┬──────────────────┬─────┬─────┬──────────┬──────────┐
│序号 │   项目名称   │     规格型号     │单位 │数量 │ 单价(元) │ 金额(元) │ (细线边框)
├─────┼──────────────┼──────────────────┼─────┼─────┼──────────┼──────────┤
│  1  │预算、结算审核│工程造价咨询服务  │ 项  │  1  │ 2,310.00 │ 2,310.00 │
├─────┴──────────────┴──────────────────┴─────┴─────┴──────────┼──────────┤
│                    合计                                      │ 2,310.00 │
└──────────────────────────────────────────────────────────────┴──────────┘
```

### 2. 文档结构
```
${projectName}最高限价编制说明

一、编制依据
...

二、限价类型
...

三、计算方法
...

四、最高限价
详见下表：

[表格插入在这里]

编制人（签字）：                 编制部门负责人（签字）：
日期：      年   月   日        日期：      年   月   日
```

## 测试验证

### 1. 测试接口
```http
GET /xjzs/projectReport/test-engineering-template
```
**预期返回**: "工程类模板生成测试成功 - 表格已插入到正确位置，使用细线边框，合计行已合并"

### 2. 实际导出测试
```http
GET /xjzs/projectReport/export-engineering-pdf?projectId=123&tableItems=[...]
```

### 3. 验证要点
- [ ] 表格位置在"四、最高限价"部分，不在文档末尾
- [ ] 表格边框为细线，不会过粗
- [ ] 合计行单元格正确合并
- [ ] 文档结构完整，格式正确

## 日志输出
成功时会输出：
```
成功插入工程类表格到正确位置，包含X行数据，使用细线边框，合计行已合并单元格
```

## 故障排除

### 1. 表格仍在文档末尾
- 检查模板文件是否包含`${projectContent}`占位符
- 确认模板中是否有"四、最高限价"文字
- 查看日志确认查找逻辑是否正确执行

### 2. 边框仍然很粗
- 确认`setTableBorders()`方法中的`setSz()`参数为2
- 检查PDF查看器的显示设置
- 尝试在不同设备上查看效果

### 3. 表格格式异常
- 确认表格数据JSON格式正确
- 检查单元格合并逻辑是否正常执行
- 查看后端日志中的错误信息

## 后续优化建议

1. **可配置边框**: 允许通过参数控制边框粗细
2. **精确定位**: 支持更精确的表格插入位置控制
3. **模板验证**: 添加模板文件格式验证
4. **预览功能**: 提供表格插入位置的预览功能
