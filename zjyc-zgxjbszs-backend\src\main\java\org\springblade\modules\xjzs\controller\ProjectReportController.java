package org.springblade.modules.xjzs.controller;

import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.docx4j.Docx4J;
import org.docx4j.fonts.IdentityPlusMapper;
import org.docx4j.fonts.PhysicalFonts;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.json.JSONArray;
import org.json.JSONObject;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblWidth;
import org.springblade.core.tool.api.R;
import org.springblade.modules.xjzs.entity.ProjectReportEntity;
import org.springblade.modules.xjzs.pojo.vo.ProjectReportCheckResultVO;
import org.springblade.modules.xjzs.service.IProjectReportService;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.modules.xjzs.util.DocxToPdfConverter;
import org.springblade.modules.xjzs.util.WordTemplateUtil;
import org.springblade.modules.xjzs.util.ReportTemplateManager;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import org.springblade.modules.dify.resp.WorkflowStreamResponse;

import java.io.*;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 项目报告控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/xjzs/projectReport")
@Tag(name = "项目报告接口")
public class ProjectReportController {

    private final IProjectReportService projectReportService;

    /**
     * 保存项目报告
     */
    @PostMapping("/save")
    @Operation(summary = "保存项目报告", description = "传入报告信息")
    public R<Boolean> save(@RequestBody ProjectReportEntity report) {
        try {
            boolean success = projectReportService.saveProjectReport(report);
            return R.data(success);
        } catch (Exception e) {
            log.error("保存项目报告失败", e);
            return R.fail("保存失败：" + e.getMessage());
        }
    }

    /**
     * 检查项目是否已存在报告
     */
    @GetMapping("/checkExists")
    @Operation(summary = "检查项目是否已存在报告", description = "传入项目ID")
    public R<Map<String, Boolean>> checkExists(@RequestParam Long projectId) {
        try {
            boolean exists = projectReportService.checkReportExists(projectId);
            Map<String, Boolean> result = new HashMap<>();
            result.put("exists", exists);
            return R.data(result);
        } catch (Exception e) {
            log.error("检查项目报告失败", e);
            return R.fail("检查失败：" + e.getMessage());
        }
    }
    /**
     * 合规校验接口
     */
    @GetMapping("/checkReport")
    @Operation(summary = "合规校验接口", description = "传入项目ID")
    public R<List<ProjectReportCheckResultVO>> checkReport(@RequestParam Long projectId) {
        try {
            List<ProjectReportCheckResultVO> vo = projectReportService.checkReport(projectId);
            projectReportService.updateReportCheckContent(projectId, JSON.toJSONString(vo));
            return R.data(vo);
        } catch (Exception e) {
            log.error("检查项目报告失败", e);
            return R.fail("检查失败：" + e.getMessage());
        }



    }

    /**
     * 新的合规性检查接口 - 使用Dify agent（流式返回）
     */
    @GetMapping("/checkComplianceWithDify")
    @Operation(summary = "使用Dify agent进行合规性检查", description = "传入项目ID")
    public Flux<WorkflowStreamResponse> checkComplianceWithDify(@RequestParam Long projectId) {
        return Flux.defer(() -> {
            try {
                // 直接返回Dify工作流的流式响应
                return projectReportService.checkComplianceWithDifyStream(projectId);

            } catch (Exception e) {
                log.error("Dify合规性检查失败", e);
                // 返回错误响应
                WorkflowStreamResponse errorResponse = new WorkflowStreamResponse();
                errorResponse.setEvent("error");
                errorResponse.setErrorInfo("合规性检查失败：" + e.getMessage());
                return Flux.just(errorResponse);
            }
        });
    }

    /**
     * 工程类合规性检查接口 - 使用Dify agent（流式返回）
     */
    @GetMapping("/checkEngineeringComplianceWithDify")
    @Operation(summary = "使用Dify agent进行工程类合规性检查", description = "传入项目ID")
    public Flux<WorkflowStreamResponse> checkEngineeringComplianceWithDify(@RequestParam Long projectId) {
        return Flux.defer(() -> {
            try {
                // 直接返回Dify工作流的流式响应
                return projectReportService.checkEngineeringComplianceWithDifyStream(projectId);

            } catch (Exception e) {
                log.error("Dify工程类合规性检查失败", e);
                // 返回错误响应
                WorkflowStreamResponse errorResponse = new WorkflowStreamResponse();
                errorResponse.setEvent("error");
                errorResponse.setErrorInfo("工程类合规性检查失败：" + e.getMessage());
                return Flux.just(errorResponse);
            }
        });
    }

    /**
     * 采购类合规性检查接口 - 使用Dify agent（流式返回）
     */
    @GetMapping("/checkProcurementComplianceWithDify")
    @Operation(summary = "使用Dify agent进行采购类合规性检查", description = "传入项目ID")
    public Flux<WorkflowStreamResponse> checkProcurementComplianceWithDify(@RequestParam Long projectId) {
        return Flux.defer(() -> {
            try {
                // 直接返回Dify工作流的流式响应
                return projectReportService.checkProcurementComplianceWithDifyStream(projectId);

            } catch (Exception e) {
                log.error("Dify采购类合规性检查失败", e);
                // 返回错误响应
                WorkflowStreamResponse errorResponse = new WorkflowStreamResponse();
                errorResponse.setEvent("error");
                errorResponse.setErrorInfo("采购类合规性检查失败：" + e.getMessage());
                return Flux.just(errorResponse);
            }
        });
    }

    /**
     * 获取项目报告详情
     */
    @GetMapping("/detail")
    @Operation(summary = "获取项目报告详情", description = "传入项目ID")
    public R<ProjectReportEntity> detail(@RequestParam Long projectId) {
        try {
            ProjectReportEntity report = projectReportService.getProjectReport(projectId);
            return R.data(report);
        } catch (Exception e) {
            log.error("获取项目报告详情失败", e);
            return R.fail("获取详情失败：" + e.getMessage());
        }
    }




    @GetMapping("/export-pdf")
    @Operation(summary = "导出项目报告为PDF", description = "传入项目ID")
    public void exportReportPdf(@RequestParam Long projectId, HttpServletResponse response,@RequestParam String tableItems,@RequestParam String tabledata) throws Exception {
        // 获取报告详情
        ProjectReportEntity report = projectReportService.getProjectReport(projectId);
        if (report == null) {
            response.setContentType("text/html;charset=utf-8");
            response.getWriter().write("请先保存报告");
            return;
        }

        // 使用模板管理器生成文档
        XWPFDocument document = ReportTemplateManager.generateReport(report, tableItems, tabledata);

        // 将 Word 文档转为 PDF
        ByteArrayOutputStream docxOutputStream = new ByteArrayOutputStream();
        document.write(docxOutputStream);
        document.close();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd_HH-mm-ss");
        String currentDate = sdf.format(new Date());

        try {
            // 使用 Docx4j 将 DOCX 转 PDF
            ByteArrayOutputStream pdfOutputStream = new ByteArrayOutputStream();

            DocxToPdfConverter.convertDocxToPdf(new ByteArrayInputStream(docxOutputStream.toByteArray()), pdfOutputStream);

            // 设置响应头
            String fileName = (report.getProject() != null ? report.getProject().getName() : "项目") + "最高限价计算报告"+currentDate+".pdf";
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "attachment; filename=" +
                    new String(fileName.getBytes("UTF-8"), "ISO-8859-1"));

            // 输出 PDF
            response.getOutputStream().write(pdfOutputStream.toByteArray());
        } catch (Exception e) {
            // 如果PDF转换失败，降级为DOCX输出
            String fileName = (report.getProject() != null ? report.getProject().getName() : "项目") + "最高限价计算报告"+currentDate+".docx";
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setHeader("Content-Disposition", "attachment; filename=" +
                    new String(fileName.getBytes("UTF-8"), "ISO-8859-1"));

            // 输出 DOCX
            response.getOutputStream().write(docxOutputStream.toByteArray());
        }
        response.getOutputStream().flush();
    }

    /**
     * 导出工程类项目报告为PDF
     */
    @GetMapping("/export-engineering-pdf")
    @Operation(summary = "导出工程类项目报告为PDF", description = "传入项目ID")
    public void exportEngineeringReportPdf(@RequestParam Long projectId, HttpServletResponse response,
                                         @RequestParam(required = false) String tableItems,
                                         @RequestParam(required = false) String tabledata) throws Exception {
        // 获取报告详情
        ProjectReportEntity report = projectReportService.getProjectReport(projectId);
        if (report == null) {
            response.setContentType("text/html;charset=utf-8");
            response.getWriter().write("请先保存报告");
            return;
        }

        // 强制设置为工程类
        report.setAlgorithmCategory("工程咨询类");

        // 使用模板管理器生成文档
        XWPFDocument document = ReportTemplateManager.generateReport(report, tableItems, tabledata);

        // 将 Word 文档转为 PDF
        ByteArrayOutputStream docxOutputStream = new ByteArrayOutputStream();
        document.write(docxOutputStream);
        document.close();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd_HH-mm-ss");
        String currentDate = sdf.format(new Date());

        try {
            // 使用 Docx4j 将 DOCX 转 PDF
            ByteArrayOutputStream pdfOutputStream = new ByteArrayOutputStream();

            DocxToPdfConverter.convertDocxToPdf(new ByteArrayInputStream(docxOutputStream.toByteArray()), pdfOutputStream);

            // 设置响应头
            String fileName = (report.getProject() != null ? report.getProject().getName() : "项目") + "工程类最高限价计算报告"+currentDate+".pdf";
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "attachment; filename=" +
                    new String(fileName.getBytes("UTF-8"), "ISO-8859-1"));

            // 输出 PDF
            response.getOutputStream().write(pdfOutputStream.toByteArray());
        } catch (Exception e) {
            // 如果PDF转换失败，降级为DOCX输出
            String fileName = (report.getProject() != null ? report.getProject().getName() : "项目") + "工程类最高限价计算报告"+currentDate+".docx";
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setHeader("Content-Disposition", "attachment; filename=" +
                    new String(fileName.getBytes("UTF-8"), "ISO-8859-1"));

            // 输出 DOCX
            response.getOutputStream().write(docxOutputStream.toByteArray());
        }
        response.getOutputStream().flush();
    }

    /**
     * 测试工程类模板生成
     */
    @GetMapping("/test-engineering-template")
    @Operation(summary = "测试工程类模板生成", description = "用于测试工程类模板功能")
    public R<String> testEngineeringTemplate() {
        try {
            // 创建测试报告数据
            ProjectReportEntity testReport = new ProjectReportEntity();
            testReport.setReportId("TEST-ENG-001");
            testReport.setAlgorithmCategory("工程咨询类");
            testReport.setTotalPrice(new BigDecimal("50000.00"));

            // 创建测试项目数据
            // 注意：这里需要根据实际的Project实体类来设置
            // testReport.setProject(testProject);

            // 测试表格数据 - 模拟你页面显示的格式，包含多行数据测试边框
            String testTableItems = "[" +
                "{\"name\":\"预算、结算审核\",\"specifications\":\"工程造价咨询服务\",\"unitPrice\":2310,\"unit\":\"项\",\"quantity\":1,\"totalPrice\":2310}," +
                "{\"name\":\"专家评审费\",\"specifications\":\"技术专家评审\",\"unitPrice\":500,\"unit\":\"人次\",\"quantity\":3,\"totalPrice\":1500}" +
                "]";
            String testTableData = "[]";

            // 生成文档
            XWPFDocument document = ReportTemplateManager.generateReport(testReport, testTableItems, testTableData);

            if (document != null) {
                document.close();
                return R.success("工程类模板生成测试成功");
            } else {
                return R.fail("工程类模板生成失败");
            }
        } catch (Exception e) {
            log.error("测试工程类模板失败", e);
            return R.fail("测试失败：" + e.getMessage());
        }
    }

}
