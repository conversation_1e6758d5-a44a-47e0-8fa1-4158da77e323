package org.springblade.modules.xjzs.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;
import org.json.JSONArray;
import org.json.JSONObject;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblWidth;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.modules.xjzs.entity.ProjectReportEntity;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 报告模板管理器
 * 用于管理不同类型的报告模板生成
 */
@Slf4j
public class ReportTemplateManager {

    /**
     * 报告类型枚举
     */
    public enum ReportType {
        TRAINING("培训类", "doctemplete/peixun.docx"),
        ENGINEERING("工程类", "doctemplete/gongcheng.docx"),
        PROCUREMENT("采购类", "doctemplete/caigou.docx");

        private final String displayName;
        private final String templatePath;

        ReportType(String displayName, String templatePath) {
            this.displayName = displayName;
            this.templatePath = templatePath;
        }

        public String getDisplayName() {
            return displayName;
        }

        public String getTemplatePath() {
            return templatePath;
        }

        /**
         * 根据算法类别获取报告类型
         */
        public static ReportType fromAlgorithmCategory(String algorithmCategory) {
            if ("工程咨询类".equals(algorithmCategory)) {
                return ENGINEERING;
            } else if ("培训类".equals(algorithmCategory)) {
                return TRAINING;
            } else {
                return PROCUREMENT; // 默认为采购类
            }
        }
    }

    /**
     * 生成报告文档
     */
    public static XWPFDocument generateReport(ProjectReportEntity report, String tableItems, String tabledata) {
        // 根据算法类别确定报告类型
        ReportType reportType = ReportType.fromAlgorithmCategory(report.getAlgorithmCategory());

        try {
            // 尝试加载模板
            XWPFDocument document = WordTemplateUtil.loadTemplate(reportType.getTemplatePath());

            // 准备占位符数据
            Map<String, String> placeholders = preparePlaceholders(report, tableItems, tabledata, reportType);

            // 替换占位符
            WordTemplateUtil.replacePlaceholders(document, placeholders);

            // 如果是工程类，需要插入表格
            if (reportType == ReportType.ENGINEERING) {
                insertEngineeringTable(document, tableItems, tabledata);
            }

            return document;
        } catch (IOException e) {
            log.error("加载{}模板失败，使用代码生成", reportType.getDisplayName(), e);
            // 如果模板加载失败，使用代码生成
            return createReportDocumentProgrammatically(report, tableItems, tabledata, reportType);
        }
    }

    /**
     * 准备占位符数据
     */
    private static Map<String, String> preparePlaceholders(ProjectReportEntity report, String tableItems, 
                                                          String tabledata, ReportType reportType) {
        Map<String, String> placeholders = new HashMap<>();
        
        // 基本信息
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日");
        String currentDate = dateFormat.format(new Date());
        String currentUser = AuthUtil.getNickName();
        
        placeholders.put("reportDate", currentDate);
        placeholders.put("reportAuthor", currentUser);
        placeholders.put("reportId", report.getReportId());
        
        // 项目信息
        if (report.getProject() != null) {
            placeholders.put("projectName", report.getProject().getName());
            placeholders.put("projectType", report.getProject().getType());
            placeholders.put("projectBudget", report.getProject().getBudget() != null ?
                    report.getProject().getBudget().toString() : "");
            placeholders.put("procurementMethod", report.getProject().getProcurementMethod());
            
            // 根据报告类型生成不同的内容
            String projectContent = buildProjectContent(tableItems, tabledata, reportType);
            placeholders.put("projectContent", projectContent);
        }
        
        // 根据报告类型设置不同的编制依据和计算方法
        setTypeSpecificContent(placeholders, reportType);
        
        // 最高限价
        BigDecimal totalPrice = report.getTotalPrice();
        if (totalPrice != null) {
            placeholders.put("totalPrice", String.format("%.2f", totalPrice));
            placeholders.put("unitPrice", String.format("%.2f", totalPrice));
            placeholders.put("discount", "0.95");
            placeholders.put("preferential", "5%");
        }
        
        return placeholders;
    }

    /**
     * 根据报告类型设置特定内容
     */
    private static void setTypeSpecificContent(Map<String, String> placeholders, ReportType reportType) {
        switch (reportType) {
            case ENGINEERING:
                placeholders.put("basisContent", "依据《工程咨询行业管理办法》（国家发展改革委2017年第9号令）、《建设工程造价咨询服务计费指导意见》（中价协〔2018〕11号）等文件，结合本项目特点，制定本限价。");
                placeholders.put("calculationMethod", "1、算法：根据《建设工程造价咨询服务计费指导意见》（中价协〔2018〕11号）的计费标准进行计算。\n" +
                        "2、计价标准：根据工程类别、工作成果、复杂程度等因素确定基础费率，并应用相应的调整系数。\n" +
                        "3、计算规则：咨询费用 = 基础费用 × 复杂系数 × 调整系数");
                placeholders.put("limitType", "本限价包括：人工费、设备费、软件费、差旅费、资料费、会议费、专家评审费等工程咨询相关费用。");
                break;
            case TRAINING:
                placeholders.put("basisContent", "依据《广东省湛江市人民政府采购中心关于印发政府采购限价管理办法（试行）》（2020年12月），《广东省湛江市人民政府采购中心关于进一步规范政府采购活动的通知》（2019年5月）等文件，结合本项目特点，制定本限价。");
                placeholders.put("calculationMethod", "直接使用（如政府、行业规定、第三方编制）、询价（平均价、中位价、最高价及其他合理的计算方式）、直线插入法计算公式、信息价（可结合价格变化趋势和合理预期）、成本计算等。");
                placeholders.put("limitType", "总价、单价、折扣率、优惠率等。");
                break;
            default: // PROCUREMENT
                placeholders.put("basisContent", "依据《广东省湛江市人民政府采购中心关于印发政府采购限价管理办法（试行）》（2020年12月），《广东省湛江市人民政府采购中心关于进一步规范政府采购活动的通知》（2019年5月）等文件，结合本项目特点，制定本限价。");
                placeholders.put("calculationMethod", "直接使用（如政府、行业规定、第三方编制）、询价（平均价、中位价、最高价及其他合理的计算方式）、直线插入法计算公式、信息价（可结合价格变化趋势和合理预期）、成本计算等。");
                placeholders.put("limitType", "总价、单价、折扣率、优惠率等。");
                break;
        }
    }

    /**
     * 根据报告类型构建项目内容
     */
    private static String buildProjectContent(String tableItems, String tabledata, ReportType reportType) {
        if (tabledata == null || tabledata.isEmpty()) {
            return "";
        }
        
        switch (reportType) {
            case TRAINING:
                return buildTrainingContent(tableItems, tabledata);
            case ENGINEERING:
                return buildEngineeringContent(tableItems, tabledata);
            default:
                return buildProcurementContent(tableItems, tabledata);
        }
    }

    /**
     * 构建培训类内容
     */
    private static String buildTrainingContent(String tableItems, String tabledata) {
        JSONArray tabledataArray = new JSONArray(tabledata);
        StringBuilder resultTable = new StringBuilder();
        
        // 添加空值检查
        if(tabledataArray.length() > 0 && tabledataArray.getJSONObject(0).has("trainingLocation")) {
            resultTable.append("培训方式:").
                    append(tabledataArray.getJSONObject(0).getString("trainingLocation"))
                    .append(",人数:").append(tabledataArray.getJSONObject(0).getInt("trainingPeople"))
                    .append("人,每日学时:").append(tabledataArray.getJSONObject(0).getInt("hoursPerDay"))
                    .append("小时,培训天数:").append(tabledataArray.getJSONObject(0).getInt("trainingDays"))
                    .append("天。\n");
        }

        JSONArray jsonArray = new JSONArray(tableItems);
        StringBuilder result = new StringBuilder();

        for (int i = 0; i < jsonArray.length(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            String name = jsonObject.getString("name");
            Integer unitPrice = jsonObject.getInt("unitPrice");
            String unit = jsonObject.getString("unit");
            Integer quantity = jsonObject.getInt("quantity");
            Integer totalPrice = jsonObject.getInt("totalPrice");

            result.append("\n").append(name).append("，标准:").append(unitPrice).append("元/").append(unit).append("，共：").append(quantity).append(unit).append("，金额:").append(totalPrice).append("元；\n");
        }

        // Calculate total
        int totalSum = 0;
        for (int i = 0; i < jsonArray.length(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            totalSum += jsonObject.getInt("totalPrice");
        }

        // Final result string
        return resultTable.toString() + result.append("\n合计：").append(totalSum).append("元").toString();
    }

    /**
     * 构建工程类内容
     */
    private static String buildEngineeringContent(String tableItems, String tabledata) {
        // 工程类内容构建逻辑 - 现在只返回简单描述，实际表格通过insertEngineeringTable方法插入
        return "详见下表：";
    }

    /**
     * 在Word文档中插入工程类表格
     */
    private static void insertEngineeringTable(XWPFDocument document, String tableItems, String tabledata) {
        if (tableItems == null || tableItems.isEmpty()) {
            return;
        }

        try {
            JSONArray jsonArray = new JSONArray(tableItems);
            if (jsonArray.length() == 0) {
                return;
            }

            // 先替换${projectContent}占位符为表格标记
            boolean foundProjectContent = false;
            for (XWPFParagraph paragraph : document.getParagraphs()) {
                String text = paragraph.getText();
                if (text != null && text.contains("${projectContent}")) {
                    // 清空段落内容
                    paragraph.getRuns().clear();
                    // 添加表格说明文字
                    XWPFRun run = paragraph.createRun();
                    run.setText("详见下表：");
                    foundProjectContent = true;
                    break;
                }
            }

            // 如果没找到${projectContent}，查找"四、最高限价"
            if (!foundProjectContent) {
                for (XWPFParagraph paragraph : document.getParagraphs()) {
                    String text = paragraph.getText();
                    if (text != null && text.contains("四、最高限价")) {
                        // 在此段落后添加一个新段落用于说明
                        XWPFParagraph newParagraph = document.createParagraph();
                        XWPFRun run = newParagraph.createRun();
                        run.setText("详见下表：");
                        foundProjectContent = true;
                        break;
                    }
                }
            }

            // 如果都没找到，在文档末尾添加
            if (!foundProjectContent) {
                XWPFParagraph titleParagraph = document.createParagraph();
                XWPFRun titleRun = titleParagraph.createRun();
                titleRun.setText("四、最高限价");
                titleRun.setBold(true);
                titleRun.setFontSize(14);

                XWPFParagraph descParagraph = document.createParagraph();
                XWPFRun descRun = descParagraph.createRun();
                descRun.setText("详见下表：");
            }

            // 创建表格（会自动添加到文档末尾）
            XWPFTable table = document.createTable();

            // 创建表头
            XWPFTableRow headerRow = table.getRow(0);
            headerRow.getCell(0).setText("序号");
            headerRow.addNewTableCell().setText("项目名称");
            headerRow.addNewTableCell().setText("规格型号");
            headerRow.addNewTableCell().setText("单位");
            headerRow.addNewTableCell().setText("数量");
            headerRow.addNewTableCell().setText("单价(元)");
            headerRow.addNewTableCell().setText("金额(元)");

            // 设置表头样式 - 只设置粗体，不添加背景色
            for (XWPFTableCell cell : headerRow.getTableCells()) {
                XWPFParagraph cellParagraph = cell.getParagraphArray(0);
                if (cellParagraph.getRuns().size() > 0) {
                    cellParagraph.getRuns().get(0).setBold(true);
                } else {
                    XWPFRun cellRun = cellParagraph.createRun();
                    cellRun.setBold(true);
                }
            }

            // 添加数据行
            int totalSum = 0;
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String name = jsonObject.optString("name", "");
                String specifications = jsonObject.optString("specifications", "工程造价咨询服务");
                String unit = jsonObject.optString("unit", "项");
                int quantity = jsonObject.optInt("quantity", 1);
                int unitPrice = jsonObject.optInt("unitPrice", 0);
                int totalPrice = jsonObject.optInt("totalPrice", unitPrice * quantity);

                XWPFTableRow dataRow = table.createRow();
                dataRow.getCell(0).setText(String.valueOf(i + 1));
                dataRow.getCell(1).setText(name);
                dataRow.getCell(2).setText(specifications);
                dataRow.getCell(3).setText(unit);
                dataRow.getCell(4).setText(String.valueOf(quantity));
                dataRow.getCell(5).setText(String.format("%.2f", (double) unitPrice));
                dataRow.getCell(6).setText(String.format("%.2f", (double) totalPrice));

                totalSum += totalPrice;
            }

            // 添加合计行
            XWPFTableRow totalRow = table.createRow();

            // 合并前6个单元格并设置"合计"文本
            totalRow.getCell(0).setText("合计");

            // 清空其他单元格的文本，但保留单元格结构
            for (int i = 1; i < 6; i++) {
                totalRow.getCell(i).setText("");
            }

            // 设置最后一列的总金额
            totalRow.getCell(6).setText(String.format("%.2f", (double) totalSum));

            // 合并单元格 - 将前6个单元格合并
            mergeCellsHorizontally(totalRow, 0, 5);

            // 设置合计行样式 - 只给有内容的单元格设置粗体
            // 第一个单元格（合计文字）
            XWPFParagraph firstCellParagraph = totalRow.getCell(0).getParagraphArray(0);
            if (firstCellParagraph.getRuns().size() > 0) {
                firstCellParagraph.getRuns().get(0).setBold(true);
            }

            // 最后一个单元格（总金额）
            XWPFParagraph lastCellParagraph = totalRow.getCell(6).getParagraphArray(0);
            if (lastCellParagraph.getRuns().size() > 0) {
                lastCellParagraph.getRuns().get(0).setBold(true);
            }

            // 设置表格样式
            table.setWidth("100%");

            // 设置表格边框（细线）
            setTableBorders(table);

            log.info("成功插入工程类表格到正确位置，包含{}行数据，使用细线边框，合计行已合并单元格", jsonArray.length());

        } catch (Exception e) {
            log.error("插入工程类表格失败", e);
        }
    }

    /**
     * 设置表格边框
     */
    private static void setTableBorders(XWPFTable table) {
        try {
            // 为每个单元格设置边框
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    // 设置单元格边框
                    cell.getCTTc().addNewTcPr().addNewTcBorders();

                    // 获取单元格边框
                    org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTcBorders cellBorders =
                        cell.getCTTc().getTcPr().getTcBorders();

                    // 设置边框样式
                    org.openxmlformats.schemas.wordprocessingml.x2006.main.STBorder.Enum borderStyle =
                        org.openxmlformats.schemas.wordprocessingml.x2006.main.STBorder.SINGLE;

                    // 上边框 - 使用细线
                    cellBorders.addNewTop().setVal(borderStyle);
                    cellBorders.getTop().setSz(BigInteger.valueOf(2)); // 改为2磅细线
                    cellBorders.getTop().setColor("000000");

                    // 下边框 - 使用细线
                    cellBorders.addNewBottom().setVal(borderStyle);
                    cellBorders.getBottom().setSz(BigInteger.valueOf(2)); // 改为2磅细线
                    cellBorders.getBottom().setColor("000000");

                    // 左边框 - 使用细线
                    cellBorders.addNewLeft().setVal(borderStyle);
                    cellBorders.getLeft().setSz(BigInteger.valueOf(2)); // 改为2磅细线
                    cellBorders.getLeft().setColor("000000");

                    // 右边框 - 使用细线
                    cellBorders.addNewRight().setVal(borderStyle);
                    cellBorders.getRight().setSz(BigInteger.valueOf(2)); // 改为2磅细线
                    cellBorders.getRight().setColor("000000");
                }
            }

        } catch (Exception e) {
            log.error("设置表格边框失败", e);
        }
    }

    /**
     * 水平合并单元格
     */
    private static void mergeCellsHorizontally(XWPFTableRow row, int fromCol, int toCol) {
        try {
            // 设置第一个单元格的合并属性
            XWPFTableCell firstCell = row.getCell(fromCol);
            if (firstCell.getCTTc().getTcPr() == null) {
                firstCell.getCTTc().addNewTcPr();
            }

            // 设置水平合并开始
            firstCell.getCTTc().getTcPr().addNewHMerge().setVal(
                org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge.RESTART);

            // 设置其他单元格为合并继续
            for (int i = fromCol + 1; i <= toCol; i++) {
                XWPFTableCell cell = row.getCell(i);
                if (cell.getCTTc().getTcPr() == null) {
                    cell.getCTTc().addNewTcPr();
                }
                cell.getCTTc().getTcPr().addNewHMerge().setVal(
                    org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge.CONTINUE);
            }

        } catch (Exception e) {
            log.error("合并单元格失败", e);
        }
    }

    /**
     * 构建采购类内容
     */
    private static String buildProcurementContent(String tableItems, String tabledata) {
        // 采购类内容构建逻辑，可以复用培训类的逻辑或自定义
        return buildTrainingContent(tableItems, tabledata);
    }

    /**
     * 如果模板不存在，使用代码生成Word文档
     */
    private static XWPFDocument createReportDocumentProgrammatically(ProjectReportEntity report, 
                                                                    String tableItems, String tabledata, 
                                                                    ReportType reportType) {
        XWPFDocument document = new XWPFDocument();
        
        // 创建标题
        XWPFParagraph titleParagraph = document.createParagraph();
        titleParagraph.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun titleRun = titleParagraph.createRun();
        String projectName = report.getProject() != null ? report.getProject().getName() : "未知项目";
        titleRun.setText(projectName + "最高限价编制说明");
        titleRun.setBold(true);
        titleRun.setFontSize(16);
        
        // 根据报告类型创建不同的内容
        createSectionsByType(document, report, tableItems, tabledata, reportType);

        // 如果是工程类，插入表格
        if (reportType == ReportType.ENGINEERING) {
            insertEngineeringTable(document, tableItems, tabledata);
        }

        // 创建签名区域
        createSignatureArea(document);

        return document;
    }

    /**
     * 根据类型创建不同的章节
     */
    private static void createSectionsByType(XWPFDocument document, ProjectReportEntity report, 
                                           String tableItems, String tabledata, ReportType reportType) {
        Map<String, String> placeholders = preparePlaceholders(report, tableItems, tabledata, reportType);
        
        // 创建各个章节
        createSection(document, "一、编制依据", placeholders.get("basisContent"));
        createSection(document, "二、限价类型", placeholders.get("limitType"));
        createSection(document, "三、计算方法", placeholders.get("calculationMethod"));
        
        // 最高限价部分
        String priceContent = buildPriceContent(report, placeholders);
        createSection(document, "四、最高限价", priceContent);
    }

    /**
     * 构建价格内容
     */
    private static String buildPriceContent(ProjectReportEntity report, Map<String, String> placeholders) {
        BigDecimal totalPrice = report.getTotalPrice();
        String projectContent = placeholders.get("projectContent");
        
        if (totalPrice != null) {
            return projectContent + "\n\n总价：" + String.format("%.2f", totalPrice) + "元";
        } else {
            return projectContent;
        }
    }

    /**
     * 创建文档章节
     */
    private static void createSection(XWPFDocument document, String title, String content) {
        // 创建章节标题
        XWPFParagraph titleParagraph = document.createParagraph();
        titleParagraph.setAlignment(ParagraphAlignment.LEFT);
        XWPFRun titleRun = titleParagraph.createRun();
        titleRun.setText(title);
        titleRun.setBold(true);
        titleRun.setFontSize(14);
        
        // 创建章节内容
        if (content != null && !content.isEmpty()) {
            XWPFParagraph contentParagraph = document.createParagraph();
            contentParagraph.setAlignment(ParagraphAlignment.LEFT);
            XWPFRun contentRun = contentParagraph.createRun();
            contentRun.setText(content);
            contentRun.setFontSize(12);
        }
    }

    /**
     * 创建签名区域
     */
    private static void createSignatureArea(XWPFDocument document) {
        // 添加空行
        document.createParagraph();
        
        // 创建表格
        XWPFTable table = document.createTable(1, 2);
        
        // 设置表格宽度
        CTTblPr tblPr = table.getCTTbl().getTblPr();
        CTTblWidth tblWidth = tblPr.isSetTblW() ? tblPr.getTblW() : tblPr.addNewTblW();
        tblWidth.setW(BigInteger.valueOf(9000));
        tblWidth.setType(org.openxmlformats.schemas.wordprocessingml.x2006.main.STTblWidth.DXA);
        
        // 填充表格内容
        XWPFTableRow row = table.getRow(0);
        
        // 编制人
        XWPFTableCell cell1 = row.getCell(0);
        XWPFParagraph paragraph1 = cell1.getParagraphArray(0);
        XWPFRun run1 = paragraph1.createRun();
        run1.setText("编制人（签字）：");
        run1.addCarriageReturn();
        run1.addCarriageReturn();
        run1.setText("日期：      年   月   日");
        
        // 编制部门负责人
        XWPFTableCell cell2 = row.getCell(1);
        XWPFParagraph paragraph2 = cell2.getParagraphArray(0);
        XWPFRun run2 = paragraph2.createRun();
        run2.setText("编制部门负责人（签字）：");
        run2.addCarriageReturn();
        run2.addCarriageReturn();
        run2.setText("日期：      年   月   日");
    }
}
